import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    // In development mode, allow all requests to pass through
    if (process.env.NODE_ENV === "development") {
      return NextResponse.next();
    }

    // In production, use normal authentication flow
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // In development mode, always authorize
        if (process.env.NODE_ENV === "development") {
          return true;
        }

        // In production, check for valid token
        return !!token;
      },
    },
  }
);

export const config = { matcher: ["/dashboard/:path*"] };
