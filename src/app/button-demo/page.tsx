"use client";

import { App<PERSON><PERSON>on } from "../components/app-button";
import {
  AppTypography,
  Highlight1,
  Highlight2,
  Highlight3,
  Highlight4,
  Highlight5,
  Highlight6,
  Heading,
  HeadingStrong,
  Title3Strong,
  Title2Strong,
  Title1Strong,
  Body4,
  Body4<PERSON>trong,
  Body3,
  Body3<PERSON>trong,
  Body2,
  Body2Strong,
  Body1,
  Body1Strong,
  Attribution,
  AttributionStrong,
} from "../components/app-typography";
import { FaGithub } from "react-icons/fa";
import { IoMdDownload } from "react-icons/io";

export default function ButtonDemoPage() {
  return (
    <div className="min-h-screen bg-back-neutral-primary p-8">
      <div className="mx-auto max-w-4xl space-y-12">
        <div className="text-center">
          <Highlight1 className="mb-4">Design System Demo</Highlight1>
          <Body3 color="secondary">
            Showcasing button and typography components from the Figma design
            system
          </Body3>
        </div>

        {/* Typography Demo */}
        <section className="space-y-8">
          <Highlight3>Typography System</Highlight3>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {/* Highlight Styles */}
            <div className="space-y-4">
              <Title2Strong>Highlight Styles</Title2Strong>
              <div className="space-y-3">
                <Highlight1>Highlight 1 (54/64)</Highlight1>
                <Highlight2>Highlight 2 (40/48)</Highlight2>
                <Highlight3>Highlight 3 (36/48)</Highlight3>
                <Highlight4>Highlight 4 (40/48)</Highlight4>
                <Highlight5>Highlight 5 (36/48)</Highlight5>
                <Highlight6>Highlight 6 (24/32)</Highlight6>
              </div>
            </div>

            {/* Heading and Title Styles */}
            <div className="space-y-4">
              <Title2Strong>Heading & Title Styles</Title2Strong>
              <div className="space-y-3">
                <Heading>Heading (18/22)</Heading>
                <HeadingStrong>Heading Strong (18/22)</HeadingStrong>
                <Title3Strong>Title 3 Strong (20/26)</Title3Strong>
                <Title2Strong>Title 2 Strong (16/22)</Title2Strong>
                <Title1Strong>Title 1 Strong (14/22)</Title1Strong>
              </div>
            </div>

            {/* Body Styles */}
            <div className="space-y-4">
              <Title2Strong>Body Styles</Title2Strong>
              <div className="space-y-3">
                <Body4>Body 4 (18/28)</Body4>
                <Body4Strong>Body 4 Strong (18/28)</Body4Strong>
                <Body3>Body 3 (16/26)</Body3>
                <Body3Strong>Body 3 Strong (16/26)</Body3Strong>
                <Body2>Body 2 (14/22)</Body2>
                <Body2Strong>Body 2 Strong (14/22)</Body2Strong>
                <Body1>Body 1 (13/20)</Body1>
                <Body1Strong>Body 1 Strong (13/20)</Body1Strong>
              </div>
            </div>

            {/* Attribution Styles */}
            <div className="space-y-4">
              <Title2Strong>Attribution Styles</Title2Strong>
              <div className="space-y-3">
                <Attribution>Attribution (11/13)</Attribution>
                <AttributionStrong>
                  Attribution Strong (11/13)
                </AttributionStrong>
              </div>
            </div>
          </div>
        </section>

        {/* Button Variants */}
        <section className="space-y-8">
          <Highlight3>Button System</Highlight3>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {/* Primary Buttons */}
            <div className="space-y-4">
              <Title2Strong>Primary</Title2Strong>
              <div className="space-y-3">
                <AppButton variant="primary">Default</AppButton>
                <AppButton variant="primary" leftIcon={<FaGithub />}>
                  With Left Icon
                </AppButton>
                <AppButton variant="primary" rightIcon={<IoMdDownload />}>
                  With Right Icon
                </AppButton>
                <AppButton
                  variant="primary"
                  leftIcon={<FaGithub />}
                  rightIcon={<IoMdDownload />}
                >
                  Both Icons
                </AppButton>
                <AppButton variant="primary" disabled>
                  Disabled
                </AppButton>
                <AppButton variant="primary" loading>
                  Loading
                </AppButton>
              </div>
            </div>

            {/* Secondary Buttons */}
            <div className="space-y-4">
              <Title2Strong>Secondary</Title2Strong>
              <div className="space-y-3">
                <AppButton variant="secondary">Default</AppButton>
                <AppButton variant="secondary" leftIcon={<FaGithub />}>
                  With Left Icon
                </AppButton>
                <AppButton variant="secondary" rightIcon={<IoMdDownload />}>
                  With Right Icon
                </AppButton>
                <AppButton
                  variant="secondary"
                  leftIcon={<FaGithub />}
                  rightIcon={<IoMdDownload />}
                >
                  Both Icons
                </AppButton>
                <AppButton variant="secondary" disabled>
                  Disabled
                </AppButton>
                <AppButton variant="secondary" loading>
                  Loading
                </AppButton>
              </div>
            </div>

            {/* Outline Buttons */}
            <div className="space-y-4">
              <Title2Strong>Outline</Title2Strong>
              <div className="space-y-3">
                <AppButton variant="outline">Default</AppButton>
                <AppButton variant="outline" leftIcon={<FaGithub />}>
                  With Left Icon
                </AppButton>
                <AppButton variant="outline" rightIcon={<IoMdDownload />}>
                  With Right Icon
                </AppButton>
                <AppButton
                  variant="outline"
                  leftIcon={<FaGithub />}
                  rightIcon={<IoMdDownload />}
                >
                  Both Icons
                </AppButton>
                <AppButton variant="outline" disabled>
                  Disabled
                </AppButton>
                <AppButton variant="outline" loading>
                  Loading
                </AppButton>
              </div>
            </div>
          </div>
        </section>

        {/* Button Sizes */}
        <section className="space-y-8">
          <Title2Strong>Button Sizes</Title2Strong>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="space-y-4">
              <Title1Strong>Default Size</Title1Strong>
              <div className="space-y-3">
                <AppButton variant="primary" size="default">
                  Default Primary
                </AppButton>
                <AppButton variant="secondary" size="default">
                  Default Secondary
                </AppButton>
                <AppButton variant="outline" size="default">
                  Default Outline
                </AppButton>
              </div>
            </div>

            <div className="space-y-4">
              <Title1Strong>Large Size</Title1Strong>
              <div className="space-y-3">
                <AppButton variant="primary" size="lg">
                  Large Primary
                </AppButton>
                <AppButton variant="secondary" size="lg">
                  Large Secondary
                </AppButton>
                <AppButton variant="outline" size="lg">
                  Large Outline
                </AppButton>
              </div>
            </div>
          </div>
        </section>

        {/* Full Width Buttons */}
        <section className="space-y-8">
          <Title2Strong>Full Width Buttons</Title2Strong>

          <div className="space-y-3">
            <AppButton variant="primary" fullWidth>
              Full Width Primary
            </AppButton>
            <AppButton variant="secondary" fullWidth>
              Full Width Secondary
            </AppButton>
            <AppButton variant="outline" fullWidth>
              Full Width Outline
            </AppButton>
          </div>
        </section>

        {/* Interactive States Demo */}
        <section className="space-y-8">
          <Title2Strong>Interactive States</Title2Strong>
          <Body2 color="secondary">
            Hover, focus, and active states are automatically handled by the
            component. Try interacting with the buttons above to see the state
            changes.
          </Body2>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <AppButton
              variant="primary"
              onClick={() => alert("Primary clicked!")}
            >
              Click Me (Primary)
            </AppButton>
            <AppButton
              variant="secondary"
              onClick={() => alert("Secondary clicked!")}
            >
              Click Me (Secondary)
            </AppButton>
            <AppButton
              variant="outline"
              onClick={() => alert("Outline clicked!")}
            >
              Click Me (Outline)
            </AppButton>
          </div>
        </section>

        {/* Color Variants Demo */}
        <section className="space-y-8">
          <Title2Strong>Typography Color Variants</Title2Strong>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="space-y-3">
              <Body2 color="primary">Primary Color Text</Body2>
              <Body2 color="secondary">Secondary Color Text</Body2>
              <Body2 color="tertiary">Tertiary Color Text</Body2>
              <Body2 color="quaternary">Quaternary Color Text</Body2>
              <Body2 color="accent">Accent Color Text</Body2>
            </div>

            <div className="space-y-3">
              <AppTypography variant="body-2" color="primary">
                Using AppTypography directly
              </AppTypography>
              <AppTypography variant="body-2" color="accent" as="span">
                As span element
              </AppTypography>
              <AppTypography variant="title-1-strong" color="primary" as="h4">
                Custom element mapping
              </AppTypography>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
