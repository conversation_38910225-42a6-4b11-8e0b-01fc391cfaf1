# AppButton Component

A flexible, reusable button component that implements the Figma design system with support for multiple variants, sizes, states, and themes.

## Features

- **Three Variants**: Primary, Secondary, and Outline
- **Two Sizes**: Default and Large
- **Theme Support**: Automatic light/dark mode adaptation
- **Interactive States**: Default, Hover, Active, Disabled, Loading
- **Icon Support**: Left and right icon slots
- **Accessibility**: Focus states and keyboard navigation
- **Full Width Option**: Expandable to container width

## Design Specifications

Based on the Figma design system:

### Colors (Light Mode)
- **Primary**: Background `#7160E8`, Hover `#5548AE`, Active `#443A8B`
- **Secondary**: Background `#DFDBFA`, Text `#5649B0`, Hover `#D2CEED`, Active `#C2BEE1`
- **Outline**: Border `#7160E8`, Text `#5649B0`, Hover border `#5548AE`, Active background `#DDDBF2`

### Colors (Dark Mode)
- **Primary**: Background `#C9C4E8`, Text `#3626A4`, Active `#B8B3D4`
- **Secondary**: Background `#343147`, Text `#DFDBFA`, Hover `#3B384F`, Active `#413D56`
- **Outline**: Border `#DFDBFA`, Text `#DFDBFA`, Hover border `#C9C4E8`, Active background `#2F2D3D`

### Typography
- **Default Size**: 14px (text-sm), font-weight: 700 (bold)
- **Large Size**: 18px (text-lg), font-weight: 700 (bold)
- **Font Family**: Blender Pro (inherited from project)

### Spacing
- **Padding**: 8px 12px (py-2 px-3)
- **Border Radius**: 8px (rounded-lg)
- **Icon Gap**: 4px (gap-1)
- **Icon Size**: 16px (w-4 h-4)

## Usage

### Basic Usage

```tsx
import { AppButton } from "./components/app-button";

// Primary button (default)
<AppButton>Click me</AppButton>

// Secondary button
<AppButton variant="secondary">Secondary</AppButton>

// Outline button
<AppButton variant="outline">Outline</AppButton>
```

### With Icons

```tsx
import { GlobeIcon, ChevronRightIcon } from "./icons";

// Left icon
<AppButton leftIcon={<GlobeIcon />}>
  With Icon
</AppButton>

// Right icon
<AppButton rightIcon={<ChevronRightIcon />}>
  Next
</AppButton>

// Both icons
<AppButton 
  leftIcon={<GlobeIcon />} 
  rightIcon={<ChevronRightIcon />}
>
  Both Icons
</AppButton>
```

### Sizes

```tsx
// Default size
<AppButton size="default">Default</AppButton>

// Large size
<AppButton size="lg">Large</AppButton>
```

### States

```tsx
// Disabled
<AppButton disabled>Disabled</AppButton>

// Loading
<AppButton loading>Loading...</AppButton>

// Full width
<AppButton fullWidth>Full Width</AppButton>
```

### Event Handling

```tsx
<AppButton 
  onClick={() => console.log('Button clicked!')}
  type="submit"
>
  Submit
</AppButton>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `"primary" \| "secondary" \| "outline"` | `"primary"` | Button style variant |
| `size` | `"default" \| "lg"` | `"default"` | Button size |
| `children` | `ReactNode` | - | Button content |
| `className` | `string` | `""` | Additional CSS classes |
| `disabled` | `boolean` | `false` | Disable the button |
| `loading` | `boolean` | `false` | Show loading state |
| `fullWidth` | `boolean` | `false` | Make button full width |
| `onClick` | `(e?: any) => void` | `noop` | Click handler |
| `type` | `"button" \| "submit" \| "reset"` | `"button"` | Button type |
| `leftIcon` | `ReactNode` | - | Icon to show on the left |
| `rightIcon` | `ReactNode` | - | Icon to show on the right |

## Theme Integration

The component automatically adapts to the current theme using `next-themes`. It detects light/dark mode and applies appropriate colors from the design system.

## Accessibility

- Focus ring with appropriate colors
- Keyboard navigation support
- Disabled state handling
- Semantic button element
- Screen reader friendly

## Migration from Old Button

The new AppButton replaces the previous implementation with:

1. **Simplified API**: Removed unused variants (ghost, danger, success, cta, login, default)
2. **Figma Compliance**: Colors and spacing match the design system exactly
3. **Better Theme Support**: Proper light/dark mode implementation
4. **Icon Integration**: Built-in icon slots instead of manual composition
5. **Improved States**: Better loading and disabled state handling

### Breaking Changes

- Removed variants: `ghost`, `danger`, `success`, `cta`, `login`, `default`
- Size names changed: `sm`, `md`, `xl` → `default`, `lg`
- Different color scheme to match Figma design
- Icon props instead of manual icon composition

## Examples

Visit `/button-demo` to see all variants, sizes, and states in action with both light and dark themes.
