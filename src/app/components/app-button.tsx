"use client";

// Simple noop function to replace lodash dependency
const noop = () => {};
import type { ReactNode } from "react";
import { cn } from "../helpers/cn";

type AppButtonVariant =
  | "primary" // Main action button (purple background)
  | "secondary" // Secondary action (light background)
  | "outline"; // Outlined button (transparent with border)

type AppButtonSize =
  | "default" // Default button (14px font, 8px 12px padding)
  | "lg"; // Large button (18px font, 8px 12px padding)

type AppButtonProps = {
  variant?: AppButtonVariant;
  size?: AppButtonSize;
  className?: string;
  children: ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (e?: any) => void;
  type?: "button" | "submit" | "reset";
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
};

export const AppButton = ({
  children,
  className = "",
  variant = "primary",
  size = "default",
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick = noop,
  type = "button",
  leftIcon,
  rightIcon,
}: AppButtonProps) => {
  // Base styles that apply to all buttons
  const baseStyles = cn(
    // Layout and spacing
    "inline-flex items-center justify-center gap-1 rounded-lg border transition-all duration-200 ease-in-out",
    "font-bold leading-tight text-center cursor-pointer",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",

    // Size variants
    {
      // Default size: 14px font, 8px 12px padding
      "px-3 py-2 text-sm": size === "default",
      // Large size: 18px font, 8px 12px padding
      "px-3 py-2 text-lg": size === "lg",
    },

    // Full width
    {
      "w-full": fullWidth,
    },

    // Disabled state
    {
      "opacity-50 cursor-not-allowed pointer-events-none": disabled || loading,
    }
  );

  // Variant-specific styles using design system tokens
  const variantStyles = cn({
    // Primary variant - uses accent colors
    "bg-accent-primary text-fore-on-accent-primary border-accent-primary hover:bg-accent-secondary active:bg-accent-tertiary focus:ring-accent-primary":
      variant === "primary",

    // Secondary variant - uses accent alt colors
    "bg-accent-alt-primary text-accent-primary border-accent-alt-primary hover:bg-accent-alt-secondary active:bg-accent-alt-tertiary focus:ring-accent-alt-primary":
      variant === "secondary",

    // Outline variant - transparent background with accent border
    "bg-transparent text-accent-primary border-accent-primary hover:border-accent-secondary active:bg-accent-alt-primary focus:ring-accent-primary":
      variant === "outline",
  });

  return (
    <button
      {...{
        disabled: disabled || loading,
        onClick,
        type,
      }}
      className={cn(baseStyles, variantStyles, className)}
    >
      {leftIcon && <span className="size-4 shrink-0">{leftIcon}</span>}

      <span className="flex-1 px-0.5">{loading ? "Loading..." : children}</span>

      {rightIcon && <span className="size-4 shrink-0">{rightIcon}</span>}
    </button>
  );
};
