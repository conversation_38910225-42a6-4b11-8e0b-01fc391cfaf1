"use client";

import noop from "lodash/noop";
import type { ReactNode } from "react";

import { cn } from "../helpers/cn";

type AppButtonVariant =
  | "primary" // Main action button (purple background)
  | "secondary" // Secondary action (dark background)
  | "outline" // Outlined button (transparent with border)
  | "ghost" // Minimal button (transparent)
  | "danger" // Destructive action (red)
  | "success" // Success action (green)
  | "cta" // Call-to-action (light purple)
  | "login" // Login style (outlined white)
  | "default"; // Default white background

type AppButtonSize =
  | "sm" // Small button
  | "md" // Medium button (default)
  | "lg" // Large button
  | "xl"; // Extra large button

type AppButtonProps = {
  variant?: AppButtonVariant;
  size?: AppButtonSize;
  className?: string;
  children: ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (e?: any) => void;
  type?: "button" | "submit" | "reset";
};

export const AppButton = ({
  children,
  className = "",
  variant = "primary",
  disabled = false,
  onClick = noop,
  type = "button",
}: AppButtonProps) => {
  // const { theme } = useTheme();
  // const isDark = theme === "dark";

  return (
    <button
      {...{
        disabled,
        onClick,
        type,
      }}
      className={cn(
        "flex gap-4 items-center leading-[17px] text-[19px] text-center font-bold  py-[13px] px-[47px] rounded-[10px] ",
        {
          "text-white bg-primary": variant === "primary",
          "text-black bg-white": variant === "default",
        },
        className
      )}
    >
      {children}
    </button>
  );
};
