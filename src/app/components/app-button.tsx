"use client";

// Simple noop function to replace lodash dependency
const noop = () => {};
import type { ReactNode } from "react";
import { useTheme } from "next-themes";

import { cn } from "../helpers/cn";
import { THEME_OPTIONS } from "../services/ThemeProvider";

type AppButtonVariant =
  | "primary" // Main action button (purple background)
  | "secondary" // Secondary action (light background)
  | "outline"; // Outlined button (transparent with border)

type AppButtonSize =
  | "default" // Default button (14px font, 8px 12px padding)
  | "lg"; // Large button (18px font, 8px 12px padding)

type AppButtonProps = {
  variant?: AppButtonVariant;
  size?: AppButtonSize;
  className?: string;
  children: ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (e?: any) => void;
  type?: "button" | "submit" | "reset";
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
};

export const AppButton = ({
  children,
  className = "",
  variant = "primary",
  size = "default",
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick = noop,
  type = "button",
  leftIcon,
  rightIcon,
}: AppButtonProps) => {
  const { theme } = useTheme();
  const isDark = theme === THEME_OPTIONS.dark;

  // Base styles that apply to all buttons
  const baseStyles = cn(
    // Layout and spacing
    "inline-flex items-center justify-center gap-1 rounded-lg border transition-all duration-200 ease-in-out",
    "font-bold leading-tight text-center cursor-pointer",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",

    // Size variants
    {
      // Default size: 14px font, 8px 12px padding
      "px-3 py-2 text-sm": size === "default",
      // Large size: 18px font, 8px 12px padding
      "px-3 py-2 text-lg": size === "lg",
    },

    // Full width
    {
      "w-full": fullWidth,
    },

    // Disabled state
    {
      "opacity-50 cursor-not-allowed pointer-events-none": disabled || loading,
    }
  );

  // Variant-specific styles based on theme
  const variantStyles = cn({
    // Primary variant
    "bg-[#7160E8] text-white border-[#7160E8] hover:bg-[#5548AE] active:bg-[#443A8B] focus:ring-[#7160E8]":
      variant === "primary" && !isDark,
    "bg-[#C9C4E8] text-[#3626A4] border-[#C9C4E8] hover:bg-[#C9C4E8] active:bg-[#B8B3D4] focus:ring-[#C9C4E8]":
      variant === "primary" && isDark,

    // Secondary variant
    "bg-[#DFDBFA] text-[#5649B0] border-[#DFDBFA] hover:bg-[#D2CEED] active:bg-[#C2BEE1] focus:ring-[#DFDBFA]":
      variant === "secondary" && !isDark,
    "bg-[#343147] text-[#DFDBFA] border-[#343147] hover:bg-[#3B384F] active:bg-[#413D56] focus:ring-[#343147]":
      variant === "secondary" && isDark,

    // Outline variant
    "bg-transparent text-[#5649B0] border-[#7160E8] hover:border-[#5548AE] active:bg-[#DDDBF2] focus:ring-[#7160E8]":
      variant === "outline" && !isDark,
    "bg-transparent text-[#DFDBFA] border-[#DFDBFA] hover:border-[#C9C4E8] active:bg-[#2F2D3D] focus:ring-[#DFDBFA]":
      variant === "outline" && isDark,
  });

  return (
    <button
      {...{
        disabled: disabled || loading,
        onClick,
        type,
      }}
      className={cn(baseStyles, variantStyles, className)}
    >
      {leftIcon && <span className="size-4 shrink-0">{leftIcon}</span>}

      <span className="flex-1 px-0.5">{loading ? "Loading..." : children}</span>

      {rightIcon && <span className="size-4 shrink-0">{rightIcon}</span>}
    </button>
  );
};
