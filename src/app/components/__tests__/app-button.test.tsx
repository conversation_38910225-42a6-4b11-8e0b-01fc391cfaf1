import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'next-themes';
import { AppButton } from '../app-button';

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({ theme: 'light' }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

const TestIcon = () => <span data-testid="test-icon">🔥</span>;

describe('AppButton', () => {
  const renderButton = (props = {}) => {
    return render(
      <ThemeProvider>
        <AppButton {...props}>Test Button</AppButton>
      </ThemeProvider>
    );
  };

  it('renders with default props', () => {
    renderButton();
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Test Button');
  });

  it('renders primary variant by default', () => {
    renderButton();
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-[#7160E8]');
  });

  it('renders secondary variant', () => {
    renderButton({ variant: 'secondary' });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-[#DFDBFA]');
  });

  it('renders outline variant', () => {
    renderButton({ variant: 'outline' });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-transparent');
  });

  it('renders large size', () => {
    renderButton({ size: 'lg' });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('text-lg');
  });

  it('renders with left icon', () => {
    renderButton({ leftIcon: <TestIcon /> });
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('renders with right icon', () => {
    renderButton({ rightIcon: <TestIcon /> });
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    renderButton({ onClick: handleClick });
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    renderButton({ loading: true });
    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('Loading...');
    expect(button).toBeDisabled();
  });

  it('shows disabled state', () => {
    renderButton({ disabled: true });
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50');
  });

  it('renders full width', () => {
    renderButton({ fullWidth: true });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });

  it('applies custom className', () => {
    renderButton({ className: 'custom-class' });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('sets correct button type', () => {
    renderButton({ type: 'submit' });
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'submit');
  });
});
