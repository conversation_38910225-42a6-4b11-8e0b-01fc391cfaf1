"use client";

import { AppButton } from "./app-button";

// Simple icons for demo
const GlobeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm5.17 6H11.6c-.15-1.75-.54-3.35-1.13-4.64C12.65 2.18 14.17 3.9 14.17 6zM8 1.5c.73 1.18 1.26 2.63 1.43 4.5H6.57C6.74 4.13 7.27 2.68 8 1.5zM1.83 6c0-2.1 1.52-3.82 3.7-4.64C4.94 2.65 4.55 4.25 4.4 6H1.83zm0 2h2.57c.15 1.75.54 3.35 1.13 4.64C3.35 11.82 1.83 10.1 1.83 8zm4.74 4.64c.59-1.29.98-2.89 1.13-4.64h1.6c.15 1.75.54 3.35 1.13 4.64C9.74 12.82 8.26 12.82 6.57 12.64zM8 14.5c-.73-1.18-1.26-2.63-1.43-4.5h2.86C9.26 11.87 8.73 13.32 8 14.5zm3.47-1.86c.59-1.29.98-2.89 1.13-4.64h2.57c0 2.1-1.52 3.82-3.7 4.64z" />
  </svg>
);

const ChevronIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M6 4l4 4-4 4V4z" />
  </svg>
);

export const ButtonDemo = () => {
  return (
    <div className="min-h-screen space-y-8 bg-white p-8 dark:bg-gray-900">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-gray-900 dark:text-white">
          Button Component Demo - Figma Design Implementation
        </h1>

        {/* Primary Buttons */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Primary Buttons
          </h2>
          <div className="flex flex-wrap gap-4">
            <AppButton variant="primary" size="default">
              Button
            </AppButton>
            <AppButton
              variant="primary"
              size="default"
              leftIcon={<GlobeIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="primary"
              size="default"
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="primary"
              size="default"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>

          <div className="flex flex-wrap gap-4">
            <AppButton variant="primary" size="lg">
              Button
            </AppButton>
            <AppButton variant="primary" size="lg" leftIcon={<GlobeIcon />}>
              Button
            </AppButton>
            <AppButton variant="primary" size="lg" rightIcon={<ChevronIcon />}>
              Button
            </AppButton>
            <AppButton
              variant="primary"
              size="lg"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>
        </section>

        {/* Secondary Buttons */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Secondary Buttons
          </h2>
          <div className="flex flex-wrap gap-4">
            <AppButton variant="secondary" size="default">
              Button
            </AppButton>
            <AppButton
              variant="secondary"
              size="default"
              leftIcon={<GlobeIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="secondary"
              size="default"
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="secondary"
              size="default"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>

          <div className="flex flex-wrap gap-4">
            <AppButton variant="secondary" size="lg">
              Button
            </AppButton>
            <AppButton variant="secondary" size="lg" leftIcon={<GlobeIcon />}>
              Button
            </AppButton>
            <AppButton
              variant="secondary"
              size="lg"
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="secondary"
              size="lg"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>
        </section>

        {/* Outline Buttons */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Outline Buttons
          </h2>
          <div className="flex flex-wrap gap-4">
            <AppButton variant="outline" size="default">
              Button
            </AppButton>
            <AppButton
              variant="outline"
              size="default"
              leftIcon={<GlobeIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="outline"
              size="default"
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
            <AppButton
              variant="outline"
              size="default"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>

          <div className="flex flex-wrap gap-4">
            <AppButton variant="outline" size="lg">
              Button
            </AppButton>
            <AppButton variant="outline" size="lg" leftIcon={<GlobeIcon />}>
              Button
            </AppButton>
            <AppButton variant="outline" size="lg" rightIcon={<ChevronIcon />}>
              Button
            </AppButton>
            <AppButton
              variant="outline"
              size="lg"
              leftIcon={<GlobeIcon />}
              rightIcon={<ChevronIcon />}
            >
              Button
            </AppButton>
          </div>
        </section>

        {/* States Demo */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Button States
          </h2>
          <div className="flex flex-wrap gap-4">
            <AppButton variant="primary">Normal</AppButton>
            <AppButton variant="primary" disabled>
              Disabled
            </AppButton>
            <AppButton variant="primary" loading>
              Loading
            </AppButton>
            <AppButton variant="primary" fullWidth>
              Full Width
            </AppButton>
          </div>
        </section>

        {/* Interactive Demo */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Interactive Demo
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Hover over buttons to see hover states. Click to see active states.
          </p>
          <div className="flex flex-wrap gap-4">
            <AppButton
              variant="primary"
              onClick={() => alert("Primary button clicked!")}
            >
              Click Me
            </AppButton>
            <AppButton
              variant="secondary"
              onClick={() => alert("Secondary button clicked!")}
            >
              Click Me
            </AppButton>
            <AppButton
              variant="outline"
              onClick={() => alert("Outline button clicked!")}
            >
              Click Me
            </AppButton>
          </div>
        </section>
      </div>
    </div>
  );
};
