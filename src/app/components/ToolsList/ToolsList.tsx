import Link from "next/link";
import GenericButton from "../GenericButton/GenericButton";

export default function ToolsList() {
  return (
    <>
      <h2 className="mb-8 pl-4 text-3xl font-bold">All Recon Tools</h2>
      <Link href="/tools/sandbox" className="m-0 mb-3 w-full p-0 text-center">
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Scaffold invariants Sandbox
        </GenericButton>
      </Link>
      <h3 className="mb-4 pl-4 text-xl">Log Parsers</h3>
      <Link href="/tools/medusa" className="m-0 mb-3 w-full p-0 text-center">
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Medusa Log to Foundry
        </GenericButton>
      </Link>
      <Link href="/tools/echidna" className="m-0 mb-3 w-full p-0 text-center">
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Echidna Log to Foundry
        </GenericButton>
      </Link>
      <Link href="/tools/halmos" className="m-0 mb-3 w-full p-0 text-center">
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Halmos Log to Foundry
        </GenericButton>
      </Link>
      <h3 className="mb-4 pl-4 text-xl">Tools</h3>
      <Link
        href="/tools/bytecode-compare"
        className="m-0 mb-3 w-full p-0 text-center"
      >
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Bytecode compare
        </GenericButton>
      </Link>
      <Link
        href="/tools/bytecode-to-interface"
        className="m-0 mb-3 w-full p-0 text-center"
      >
        <GenericButton className={"button_login m-0 w-full p-0"}>
          Bytecode to interface
        </GenericButton>
      </Link>
    </>
  );
}
