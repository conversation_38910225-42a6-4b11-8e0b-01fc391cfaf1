import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAPIAuth } from "@/lib/dev-auth";

export async function GET(req: NextRequest) {
  const auth = await getAPIAuth(req);

  if (!auth.isAuthenticated) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  let foundData;
  try {
    // Grab the org data
    foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/organizations/my`,
      headers: { Authorization: `Bearer ${auth.jwt?.access_token as string}` },
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      // NOTE: CUSTOM: If we return 404, we know it's because we don't belong, it's not an error
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}
