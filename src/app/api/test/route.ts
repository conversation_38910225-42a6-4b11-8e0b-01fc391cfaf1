import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAPIAuth } from "@/lib/dev-auth";

export async function GET(req: NextRequest) {
  const auth = await getAPIAuth(req);

  if (!auth.isAuthenticated) {
    return NextResponse.json("Not logged in");
  }

  if (
    process.env.NODE_ENV != "development" &&
    process.env.ALEX_SETTING == "true"
  ) {
    throw new Error("NEVER IN PROD!!");
  }

  return NextResponse.json(auth.jwt);
}
