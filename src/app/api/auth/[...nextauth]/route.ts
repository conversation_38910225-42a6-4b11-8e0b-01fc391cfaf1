import type { NextAuthOptions } from "next-auth";
import Next<PERSON><PERSON> from "next-auth";
import type { JWT } from "next-auth/jwt";
import GitHubProvider from "next-auth/providers/github";
import axios from "axios";
import config from "@/config";
import { isDevelopmentMode, mockJWT } from "@/lib/dev-auth";

// Track ongoing refresh operations
const refreshTokenLocks = new Map<string, Promise<JWT>>();

async function refreshAccessToken(token: JWT): Promise<JWT> {
  const userId = token.sub as string;

  // If there's already a refresh operation in progress for this user, wait for it
  if (refreshTokenLocks.has(userId)) {
    try {
      return await refreshTokenLocks.get(userId);
    } catch (error) {
      refreshTokenLocks.delete(userId);
      throw error;
    }
  }

  // Create new refresh operation
  const refreshOperation = (async () => {
    try {
      const response = await axios.post(
        "https://github.com/login/oauth/access_token",
        {
          client_id: config.github.oauth.clientId as string,
          client_secret: config.github.oauth.secret as string,
          refresh_token: token.refresh_token as string,
          grant_type: "refresh_token",
        },
        {
          headers: {
            Accept: "application/json",
          },
        }
      );

      if (response.data && response.data.error) {
        throw response.data.error_description;
      }

      const { access_token, refresh_token, expires_in } = response.data;
      return {
        ...token,
        access_token,
        access_token_expires:
          Math.floor(new Date().getTime() / 1000) + expires_in,
        refresh_token: refresh_token ?? token.refresh_token,
      };
    } catch (error) {
      console.log("error", error);
      return {
        ...token,
        error: "RefreshAccessTokenError",
      };
    } finally {
      // Clean up the lock after the operation is complete
      setTimeout(() => refreshTokenLocks.delete(userId), 1000);
    }
  })();

  // Store the operation
  refreshTokenLocks.set(userId, refreshOperation);
  return refreshOperation;
}

const authOptions: NextAuthOptions = {
  providers: [
    GitHubProvider({
      clientId: config.github.oauth.clientId,
      clientSecret: config.github.oauth.secret,
    }),
  ],
  pages: {
    signIn: "/auth/signin", // Custom sign-in page
  },
  callbacks: {
    jwt: async ({ token, account, profile }): Promise<JWT> => {
      // In development mode, return mock JWT if no real token exists
      if (isDevelopmentMode() && !token.sub && !account) {
        return mockJWT;
      }

      if (account && profile) {
        return {
          ...token,
          access_token: account.access_token as string,
          access_token_expires: account.expires_at as number,
          refresh_token: account.refresh_token as string,
          account,
          profile,
        };
      }

      if (token.access_token_expires) {
        if (Date.now() / 1000 < (token.access_token_expires as number)) {
          return token;
        }
      }

      return refreshAccessToken(token);
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
