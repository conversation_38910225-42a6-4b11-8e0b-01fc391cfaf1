import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAPIAuth } from "@/lib/dev-auth";

// CREATE A MEDUSA JOB (Permissions are checked in the Server)
export async function POST(req: NextRequest) {
  const auth = await getAPIAuth(req);

  if (!auth.isAuthenticated) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  // For some reason we need to do this
  // Dumb af
  const body = await req.json();

  const {
    orgName,
    repoName,
    ref,
    directory,
    fuzzerArgs,
    preprocess,
    label,
    recipeId,
  } = body;
  let foundData;
  try {
    foundData = await axios({
      method: "POST",
      url: `${process.env.BACKEND_API_URL}/jobs/medusa`,
      headers: { Authorization: `Bearer ${auth.jwt?.access_token as string}` },
      data: {
        orgName,
        repoName,
        ref,
        directory,
        fuzzerArgs,
        preprocess,
        label,
        recipeId,
      },
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}
