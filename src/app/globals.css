@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  min-height: 100vh;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Design System Color Tokens - Light Theme */
  /* Accent Colors */
  --fill-accent-primary: #7160e8;
  --fill-accent-secondary: #5548ae;
  --fill-accent-tertiary: #443a8b;
  --fill-accent-alt-primary: #dfdbfa;
  --fill-accent-alt-secondary: #d2ceed;
  --fill-accent-alt-tertiary: #c2bee1;

  /* Foreground Colors */
  --fore-on-accent-primary: #ffffff;
  --fore-on-accent-secondary: #ffffff;
  --fore-on-accent-tertiary: #ffffff;
  --fore-neutral-primary: #000000;
  --fore-neutral-secondary: rgba(0, 0, 0, 0.8);
  --fore-neutral-tertiary: rgba(0, 0, 0, 0.75);
  --fore-neutral-quaternary: rgba(0, 0, 0, 0.6);

  /* Background Colors */
  --back-neutral-primary: #fafafa;
  --back-neutral-secondary: #f5f5f5;
  --back-neutral-tertiary: #ededed;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(0, 0, 0, 0.1);

  /* Legacy colors for backward compatibility */
  --primary-color: #7160e8;
  --secondary-color: #171717;
  --accent-color: #dfdbfa;
  --danger-color: #f54f4f;
  --success-color: #41d98d;
  --light-text-color: #fff;
  --dark-text-color: var(--secondary-color);
  --color-bg1: rgb(0, 0, 0);
  --color-bg2: rgb(0, 17, 82);
  --primary: #7160e8;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
  }
}

.light {
  /* Design System Color Tokens - Light Theme Override */
  /* Accent Colors */
  --fill-accent-primary: #7160e8;
  --fill-accent-secondary: #5548ae;
  --fill-accent-tertiary: #443a8b;
  --fill-accent-alt-primary: #dfdbfa;
  --fill-accent-alt-secondary: #d2ceed;
  --fill-accent-alt-tertiary: #c2bee1;

  /* Foreground Colors */
  --fore-on-accent-primary: #ffffff;
  --fore-on-accent-secondary: #ffffff;
  --fore-on-accent-tertiary: #ffffff;
  --fore-neutral-primary: #000000;
  --fore-neutral-secondary: rgba(0, 0, 0, 0.8);
  --fore-neutral-tertiary: rgba(0, 0, 0, 0.75);
  --fore-neutral-quaternary: rgba(0, 0, 0, 0.6);

  /* Background Colors */
  --back-neutral-primary: #fafafa;
  --back-neutral-secondary: #f5f5f5;
  --back-neutral-tertiary: #ededed;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(0, 0, 0, 0.1);

  /* Legacy theme variables */
  --primary: #7160e8;
  --primary-bg: #e7e0f6;
  --text-secondary: #2d2d2d;
  --grey-secondary: #8a8a8a;
  --block-bg: #eaedf4;
  --aside: #fff;
  --input-bg: #fcfcfc;
  --divider: #bec6da;
  --success: #259465;
  --error: #ff5252;
  --text-primary: #2b2b2b;
  --dashboard-bg: #f3f4f8;
  --logoColor: #7160e8;
}

.dark {
  /* Design System Color Tokens - Dark Theme */
  /* Accent Colors */
  --fill-accent-primary: #dfdbfa;
  --fill-accent-secondary: #c9c4e8;
  --fill-accent-tertiary: #b8b3d4;
  --fill-accent-alt-primary: #343147;
  --fill-accent-alt-secondary: #3b384f;
  --fill-accent-alt-tertiary: #413d56;

  /* Foreground Colors */
  --fore-on-accent-primary: #5649b0;
  --fore-on-accent-secondary: #3626a4;
  --fore-on-accent-tertiary: #291d7b;
  --fore-neutral-primary: #ffffff;
  --fore-neutral-secondary: rgba(255, 255, 255, 0.8);
  --fore-neutral-tertiary: rgba(255, 255, 255, 0.75);
  --fore-neutral-quaternary: rgba(255, 255, 255, 0.6);

  /* Background Colors */
  --back-neutral-primary: #1b1a19;
  --back-neutral-secondary: #262626;
  --back-neutral-tertiary: #2a2a2a;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(255, 255, 255, 0.1);

  /* Legacy theme variables */
  --primary: #dfdbfa;
  --primary-bg: #261a40;
  --text-secondary: #737373;
  --grey-secondary: #8a8a8a;
  --block-bg: linear-gradient(95.95deg, #303030 35.62%, #1e1e1e 113.48%);
  --aside: #171717;
  --input-bg: #0e0e0e;
  --divider: #4e4e4e;
  --success: #259465;
  --error: #ff5252;
  --text-primary: #fff;
  --dashboard-bg: #1e1e1e;
  --logoColor: #fff;
}

body {
  color: rgb(var(--foreground-rgb));
  margin: 0;
  padding: 0;
}

.app-button-default {
  background: white;
  color: black;
}

.landing-info-block {
  background: linear-gradient(
    288deg,
    rgba(30, 13, 66, 0.67) -21.63%,
    rgba(23, 23, 23, 0.67) 92%
  );
}

.landing-footer-block {
  background: linear-gradient(96.89deg, #5c25d2 33.34%, #4700de 122.54%);
}

.dark .gradient-dark-bg {
  background-image: linear-gradient(95.95deg, #303030 35.62%, #1e1e1e 113.48%);
}

.aside-menu::-webkit-scrollbar {
  display: none;
}
.aside-menu {
  position: sticky;
  top: 0;
  max-height: 100vh;
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.main-container {
  background-color: black;
}

.main-title-custom {
  background: linear-gradient(277.21deg, #5100ff -13.33%, #ffffff 51.57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sub-title-custom {
  background: linear-gradient(277.04deg, #5100ff 16.04%, #ffffff 51.63%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 40px;
  display: inline-block;
}

/*****************************
* PREVIOUS STYLE *
* DELETE WHEN MERGING INTO MAIN *
*****************************/

.main-title-custom-prev {
  background: linear-gradient(277.21deg, #5100ff -13.33%, #ffffff 51.57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.landing-info-block-prev {
  background: linear-gradient(
    288deg,
    rgba(30, 13, 66, 0.67) -21.63%,
    rgba(23, 23, 23, 0.67) 92%
  );
}

.landing-footer-block-prev {
  background: linear-gradient(96.89deg, #5c25d2 33.34%, #4700de 122.54%);
}

.gradient-bg-prev {
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(40deg, var(--color-bg1), var(--color-bg2));
  top: 0;
  left: 0;
  z-index: 0;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
