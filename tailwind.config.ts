import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: "class",
  content: ["./src/app/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        "accent-primary": "var(--fill-accent-primary)",
        "accent-secondary": "var(--fill-accent-secondary)",
        "accent-tertiary": "var(--fill-accent-tertiary)",
        "accent-alt-primary": "var(--fill-accent-alt-primary)",
        "accent-alt-secondary": "var(--fill-accent-alt-secondary)",
        "accent-alt-tertiary": "var(--fill-accent-alt-tertiary)",

        "fore-on-accent-primary": "var(--fore-on-accent-primary)",
        "fore-on-accent-secondary": "var(--fore-on-accent-secondary)",
        "fore-on-accent-tertiary": "var(--fore-on-accent-tertiary)",
        "fore-neutral-primary": "var(--fore-neutral-primary)",
        "fore-neutral-secondary": "var(--fore-neutral-secondary)",
        "fore-neutral-tertiary": "var(--fore-neutral-tertiary)",
        "fore-neutral-quaternary": "var(--fore-neutral-quaternary)",

        "back-neutral-primary": "var(--back-neutral-primary)",
        "back-neutral-secondary": "var(--back-neutral-secondary)",
        "back-neutral-tertiary": "var(--back-neutral-tertiary)",

        "stroke-neutral-decorative": "var(--stroke-neutral-decorative)",

        "button-primary-default-bg": "var(--button-primary-default-bg)",
        "button-primary-default-text": "var(--button-primary-default-text)",
        "button-primary-hover-bg": "var(--button-primary-hover-bg)",
        "button-primary-hover-text": "var(--button-primary-hover-text)",
        "button-primary-pressed-bg": "var(--button-primary-pressed-bg)",
        "button-primary-pressed-text": "var(--button-primary-pressed-text)",


        "button-secondary-default-bg": "var(--button-secondary-default-bg)",
        "button-secondary-default-text": "var(--button-secondary-default-text)",
        "button-secondary-hover-bg": "var(--button-secondary-hover-bg)",
        "button-secondary-hover-text": "var(--button-secondary-hover-text)",
        "button-secondary-pressed-bg": "var(--button-secondary-pressed-bg)",
        "button-secondary-pressed-text": "var(--button-secondary-pressed-text)",


        "button-outline-default-bg": "var(--button-outline-default-bg)",
        "button-outline-default-text": "var(--button-outline-default-text)",
        "button-outline-default-border": "var(--button-outline-default-border)",
        "button-outline-hover-bg": "var(--button-outline-hover-bg)",
        "button-outline-hover-text": "var(--button-outline-hover-text)",
        "button-outline-hover-border": "var(--button-outline-hover-border)",
        "button-outline-pressed-bg": "var(--button-outline-pressed-bg)",
        "button-outline-pressed-text": "var(--button-outline-pressed-text)",
        "button-outline-pressed-border": "var(--button-outline-pressed-border)",


        primary: "var(--primary)",
        primaryBg: "var(--primary-bg)",
        textSecondary: "var(--text-secondary)",
        greySecondary: "var(--grey-secondary)",
        blockBg: "var(--block-bg)",
        aside: "var(--aside)",
        inputBg: "var(--input-bg)",
        divider: "var(--divider)",
        success: "var(--success)",
        error: "var(--error)",
        primaryGradient: "var(--primary-gradient)",
        textPrimary: "var(--text-primary)",
        dashboardBG: "var(--dashboard-bg)",
      },
      fontSize: {

        "highlight-1": [
          "var(--typo-highlight-1-size)",
          {
            lineHeight: "var(--typo-highlight-1-line-height)",
            fontWeight: "var(--typo-highlight-1-weight)",
          },
        ],
        "highlight-2": [
          "var(--typo-highlight-2-size)",
          {
            lineHeight: "var(--typo-highlight-2-line-height)",
            fontWeight: "var(--typo-highlight-2-weight)",
          },
        ],
        "highlight-3": [
          "var(--typo-highlight-3-size)",
          {
            lineHeight: "var(--typo-highlight-3-line-height)",
            fontWeight: "var(--typo-highlight-3-weight)",
          },
        ],
        "highlight-4": [
          "var(--typo-highlight-4-size)",
          {
            lineHeight: "var(--typo-highlight-4-line-height)",
            fontWeight: "var(--typo-highlight-4-weight)",
          },
        ],
        "highlight-5": [
          "var(--typo-highlight-5-size)",
          {
            lineHeight: "var(--typo-highlight-5-line-height)",
            fontWeight: "var(--typo-highlight-5-weight)",
          },
        ],
        "highlight-6": [
          "var(--typo-highlight-6-size)",
          {
            lineHeight: "var(--typo-highlight-6-line-height)",
            fontWeight: "var(--typo-highlight-6-weight)",
          },
        ],

        heading: [
          "var(--typo-heading-size)",
          {
            lineHeight: "var(--typo-heading-line-height)",
            fontWeight: "var(--typo-heading-weight)",
          },
        ],
        "heading-strong": [
          "var(--typo-heading-strong-size)",
          {
            lineHeight: "var(--typo-heading-strong-line-height)",
            fontWeight: "var(--typo-heading-strong-weight)",
          },
        ],

        "title-3-strong": [
          "var(--typo-title-3-strong-size)",
          {
            lineHeight: "var(--typo-title-3-strong-line-height)",
            fontWeight: "var(--typo-title-3-strong-weight)",
          },
        ],
        "title-2-strong": [
          "var(--typo-title-2-strong-size)",
          {
            lineHeight: "var(--typo-title-2-strong-line-height)",
            fontWeight: "var(--typo-title-2-strong-weight)",
          },
        ],
        "title-1-strong": [
          "var(--typo-title-1-strong-size)",
          {
            lineHeight: "var(--typo-title-1-strong-line-height)",
            fontWeight: "var(--typo-title-1-strong-weight)",
          },
        ],

        "body-4": [
          "var(--typo-body-4-size)",
          {
            lineHeight: "var(--typo-body-4-line-height)",
            fontWeight: "var(--typo-body-4-weight)",
          },
        ],
        "body-4-strong": [
          "var(--typo-body-4-strong-size)",
          {
            lineHeight: "var(--typo-body-4-strong-line-height)",
            fontWeight: "var(--typo-body-4-strong-weight)",
          },
        ],
        "body-3": [
          "var(--typo-body-3-size)",
          {
            lineHeight: "var(--typo-body-3-line-height)",
            fontWeight: "var(--typo-body-3-weight)",
          },
        ],
        "body-3-strong": [
          "var(--typo-body-3-strong-size)",
          {
            lineHeight: "var(--typo-body-3-strong-line-height)",
            fontWeight: "var(--typo-body-3-strong-weight)",
          },
        ],
        "body-2": [
          "var(--typo-body-2-size)",
          {
            lineHeight: "var(--typo-body-2-line-height)",
            fontWeight: "var(--typo-body-2-weight)",
          },
        ],
        "body-2-strong": [
          "var(--typo-body-2-strong-size)",
          {
            lineHeight: "var(--typo-body-2-strong-line-height)",
            fontWeight: "var(--typo-body-2-strong-weight)",
          },
        ],
        "body-1": [
          "var(--typo-body-1-size)",
          {
            lineHeight: "var(--typo-body-1-line-height)",
            fontWeight: "var(--typo-body-1-weight)",
          },
        ],
        "body-1-strong": [
          "var(--typo-body-1-strong-size)",
          {
            lineHeight: "var(--typo-body-1-strong-line-height)",
            fontWeight: "var(--typo-body-1-strong-weight)",
          },
        ],

        attribution: [
          "var(--typo-attribution-size)",
          {
            lineHeight: "var(--typo-attribution-line-height)",
            fontWeight: "var(--typo-attribution-weight)",
          },
        ],
        "attribution-strong": [
          "var(--typo-attribution-strong-size)",
          {
            lineHeight: "var(--typo-attribution-strong-line-height)",
            fontWeight: "var(--typo-attribution-strong-weight)",
          },
        ],
      },
      fontFamily: {
        "design-system": "var(--typo-font-family)",
      },
    },
  },
};
export default config;
