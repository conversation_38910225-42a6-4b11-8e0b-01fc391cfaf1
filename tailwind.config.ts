import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: "class",
  content: ["./src/app/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        /* Design System Color Tokens */
        /* Accent Colors */
        "accent-primary": "var(--fill-accent-primary)",
        "accent-secondary": "var(--fill-accent-secondary)",
        "accent-tertiary": "var(--fill-accent-tertiary)",
        "accent-alt-primary": "var(--fill-accent-alt-primary)",
        "accent-alt-secondary": "var(--fill-accent-alt-secondary)",
        "accent-alt-tertiary": "var(--fill-accent-alt-tertiary)",

        /* Foreground Colors */
        "fore-on-accent-primary": "var(--fore-on-accent-primary)",
        "fore-on-accent-secondary": "var(--fore-on-accent-secondary)",
        "fore-on-accent-tertiary": "var(--fore-on-accent-tertiary)",
        "fore-neutral-primary": "var(--fore-neutral-primary)",
        "fore-neutral-secondary": "var(--fore-neutral-secondary)",
        "fore-neutral-tertiary": "var(--fore-neutral-tertiary)",
        "fore-neutral-quaternary": "var(--fore-neutral-quaternary)",

        /* Background Colors */
        "back-neutral-primary": "var(--back-neutral-primary)",
        "back-neutral-secondary": "var(--back-neutral-secondary)",
        "back-neutral-tertiary": "var(--back-neutral-tertiary)",

        /* Stroke Colors */
        "stroke-neutral-decorative": "var(--stroke-neutral-decorative)",

        /* Legacy colors for backward compatibility */
        primary: "var(--primary)",
        primaryBg: "var(--primary-bg)",
        textSecondary: "var(--text-secondary)",
        greySecondary: "var(--grey-secondary)",
        blockBg: "var(--block-bg)",
        aside: "var(--aside)",
        inputBg: "var(--input-bg)",
        divider: "var(--divider)",
        success: "var(--success)",
        error: "var(--error)",
        primaryGradient: "var(--primary-gradient)",
        textPrimary: "var(--text-primary)",
        dashboardBG: "var(--dashboard-bg)",
      },
    },
  },
};
export default config;
